# requirements.txt records the full set of dependencies for development
accelerate
codetiming
datasets
dill
flash-attn
hydra-core
liger-kernel
numpy
pandas
peft
pyarrow>=15.0.0
pybind11
pylatexenc
ray[data,train,tune,serve]
tensordict<0.6
torchdata
transformers
vllm<=0.6.3
wandb
pathvalidate
smolagents
mammoth
pdfminer
python-pptx
puremagic
pydub
SpeechRecognition
PyPDF2
youtube_transcript_api
serpapi
tqdm
pubchempy
Bio
scikit-learn
google_search_results
markdownify
numexpr
openai
openpyxl
python-dotenv
chess
sympy
xlrd
huggingface_hub
Requests
beautifulsoup4
Pillow
modelscope
html2text
pathvalidate
smolagents
pdfminer
mammoth
pdfminer.six
python-pptx
pydub
puremagic
SpeechRecognition
youtube_transcript_api
html2text
flask
psutil
pymongo
