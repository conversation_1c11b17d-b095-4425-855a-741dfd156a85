#!/usr/bin/env python3
"""
Wikipedia Section列表格式使用示例

展示如何使用优化后的Wikipedia爬虫获取<SECTION_NAME>:<SECTION_CONTENT>格式的内容列表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrl.handler.web_search_agent.web_search_agent import WikiAgent

def demo_sections_list():
    """演示section列表功能"""
    
    # 创建WikiAgent实例
    wiki_agent = WikiAgent(client=None, config=None)
    
    # 测试实体
    entity_name = "Machine Learning"
    alias_names = ["ML", "Machine learning"]
    
    print("=" * 60)
    print("Wikipedia Section列表格式演示")
    print("=" * 60)
    print(f"查询实体: {entity_name}")
    print(f"别名: {alias_names}")
    print()
    
    # 方法1: 直接获取section列表
    print("方法1: 使用 get_wikipedia_sections_list()")
    print("-" * 40)
    
    sections_list = wiki_agent.get_wikipedia_sections_list(entity_name, alias_names)
    
    if sections_list:
        print(f"✓ 成功获取 {len(sections_list)} 个sections\n")
        
        # 显示所有section
        for i, section in enumerate(sections_list, 1):
            if ':' in section:
                section_name, section_content = section.split(':', 1)
                content_length = len(section_content.strip())
                
                print(f"{i}. {section_name}")
                print(f"   内容长度: {content_length} 字符")
                
                # 显示内容预览（前100字符）
                preview = section_content.strip()[:100].replace('\n', ' ')
                print(f"   预览: {preview}...")
                print()
    else:
        print("✗ 未找到sections")
    
    # 方法2: 使用search_wiki方法
    print("\n方法2: 使用 search_wiki(return_sections_list=True)")
    print("-" * 40)
    
    result = wiki_agent.search_wiki(entity_name, alias_names, return_sections_list=True)
    
    print(f"实体: {result['entity']}")
    print(f"状态: {result['status']}")
    
    if result['sections_list']:
        print(f"Sections数量: {len(result['sections_list'])}")
        
        # 显示section名称列表
        section_names = []
        for section in result['sections_list']:
            if ':' in section:
                section_name = section.split(':', 1)[0]
                section_names.append(section_name)
        
        print(f"Section名称: {', '.join(section_names[:5])}")
        if len(section_names) > 5:
            print(f"... 还有 {len(section_names) - 5} 个")

def demo_sections_processing():
    """演示如何处理section列表数据"""
    
    wiki_agent = WikiAgent(client=None, config=None)
    
    print("\n" + "=" * 60)
    print("Section列表数据处理演示")
    print("=" * 60)
    
    # 获取数据
    sections_list = wiki_agent.get_wikipedia_sections_list("Python programming language", ["Python"])
    
    if not sections_list:
        print("未获取到数据")
        return
    
    print(f"获取到 {len(sections_list)} 个sections\n")
    
    # 处理1: 转换为字典格式
    print("处理1: 转换为字典格式")
    print("-" * 30)
    
    sections_dict = {}
    for section in sections_list:
        if ':' in section:
            section_name, section_content = section.split(':', 1)
            sections_dict[section_name.strip('<>')] = section_content.strip()
    
    print(f"字典包含 {len(sections_dict)} 个sections:")
    for name in list(sections_dict.keys())[:3]:
        content_length = len(sections_dict[name])
        print(f"  '{name}': {content_length} 字符")
    
    # 处理2: 按内容长度排序
    print("\n处理2: 按内容长度排序")
    print("-" * 30)
    
    sections_with_length = []
    for section in sections_list:
        if ':' in section:
            section_name, section_content = section.split(':', 1)
            sections_with_length.append({
                'name': section_name.strip('<>'),
                'content': section_content.strip(),
                'length': len(section_content.strip())
            })
    
    # 按长度排序
    sections_with_length.sort(key=lambda x: x['length'], reverse=True)
    
    print("最长的3个sections:")
    for i, section in enumerate(sections_with_length[:3], 1):
        print(f"  {i}. {section['name']}: {section['length']} 字符")
    
    # 处理3: 搜索特定关键词
    print("\n处理3: 搜索包含特定关键词的sections")
    print("-" * 30)
    
    keyword = "history"
    matching_sections = []
    
    for section in sections_list:
        if ':' in section:
            section_name, section_content = section.split(':', 1)
            if keyword.lower() in section_name.lower() or keyword.lower() in section_content.lower():
                matching_sections.append(section_name.strip('<>'))
    
    if matching_sections:
        print(f"包含关键词 '{keyword}' 的sections:")
        for name in matching_sections:
            print(f"  - {name}")
    else:
        print(f"未找到包含关键词 '{keyword}' 的sections")

def demo_batch_processing():
    """演示批量处理多个实体"""
    
    wiki_agent = WikiAgent(client=None, config=None)
    
    print("\n" + "=" * 60)
    print("批量处理演示")
    print("=" * 60)
    
    # 要处理的实体列表
    entities = [
        ("Artificial Intelligence", ["AI"]),
        ("Machine Learning", ["ML"]),
        ("Deep Learning", [])
    ]
    
    all_sections_data = {}
    
    for entity_name, alias_names in entities:
        print(f"处理: {entity_name}")
        
        sections_list = wiki_agent.get_wikipedia_sections_list(entity_name, alias_names)
        
        if sections_list:
            # 统计信息
            total_content_length = 0
            section_names = []
            
            for section in sections_list:
                if ':' in section:
                    section_name, section_content = section.split(':', 1)
                    section_names.append(section_name.strip('<>'))
                    total_content_length += len(section_content.strip())
            
            all_sections_data[entity_name] = {
                'sections_count': len(sections_list),
                'total_length': total_content_length,
                'section_names': section_names,
                'sections_list': sections_list
            }
            
            print(f"  ✓ {len(sections_list)} sections, {total_content_length} 字符")
        else:
            print(f"  ✗ 未找到数据")
    
    # 汇总统计
    print(f"\n汇总统计:")
    print(f"成功处理: {len(all_sections_data)} 个实体")
    
    total_sections = sum(data['sections_count'] for data in all_sections_data.values())
    total_content = sum(data['total_length'] for data in all_sections_data.values())
    
    print(f"总sections数: {total_sections}")
    print(f"总内容长度: {total_content} 字符")
    
    # 找出最常见的section名称
    all_section_names = []
    for data in all_sections_data.values():
        all_section_names.extend(data['section_names'])
    
    from collections import Counter
    common_sections = Counter(all_section_names).most_common(5)
    
    print(f"\n最常见的section名称:")
    for name, count in common_sections:
        print(f"  {name}: {count} 次")

if __name__ == "__main__":
    try:
        demo_sections_list()
        demo_sections_processing()
        demo_batch_processing()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("主要特点:")
        print("✓ 格式: <SECTION_NAME>:<SECTION_CONTENT>")
        print("✓ 自动清理HTML和引用")
        print("✓ 保留段落结构")
        print("✓ 过滤无意义内容")
        print("✓ 支持批量处理")
        print("✓ 易于后续数据处理")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
