# WebSearchAgent 结果加入 Rollout 的完整流程

## 概述

WebSearchAgent 的搜索结果通过一系列数据结构和处理步骤，最终被整合到系统的 rollout 流程中。本文档详细说明了这个数据流转过程。

## 数据流转架构图

```
WebSearchAgent.search_web_batch()
           ↓
    List[List[WebPageInfo]]
           ↓
    SearchResultInfo 包装
           ↓
      ActionInfo 存储
           ↓
   Handler.id_to_context
           ↓
    返回给调用方 (rollout)
```

## 详细流程分析

### 1. WebSearchAgent 搜索阶段

#### 输入
```python
# 在 Handler.handle_tool_call() 中调用
search_query_list = arguments["query"]  # 来自工具调用参数
user_query = question                   # 用户原始问题
api_result_dict = self.api_result_dict  # 搜索API结果缓存
```

#### 执行搜索
```python
web_page_info_list_batch = self.web_search_agent.search_web_batch(
    user_query=question, 
    search_query_list=search_query_list, 
    api_result_dict=api_result_dict
)
```

#### 输出结构
```python
# 返回: List[List[WebPageInfo]]
[
    [WebPageInfo, WebPageInfo, ...],  # 第一个搜索查询的结果
    [WebPageInfo, WebPageInfo, ...],  # 第二个搜索查询的结果
    # ...
]
```

### 2. 数据包装阶段

#### SearchResultInfo 包装
```python
search_result_info_list = [
    SearchResultInfo(
        search_query=search_query_list[j],
        web_page_info_list=web_page_info_list
    ) for j, web_page_info_list in enumerate(web_page_info_list_batch)
]
```

#### SearchResultInfo 结构
```python
class SearchResultInfo:
    def __init__(self, search_query, web_page_info_list: List[WebPageInfo]):
        self.search_query: str                      # 搜索查询
        self.web_page_info_list: List[WebPageInfo]  # 网页信息列表
        self.web_select_info_list: List[WebSelectInfo] = []  # 网页选择信息
```

### 3. ActionInfo 存储阶段

#### 创建 ActionInfo
```python
cur_action_info = ActionInfo(
    user_query=question,
    search_thinking=search_thinking,
    search_query_list=search_query_list,
    search_result_info_list=search_result_info_list
)
```

#### ActionInfo 完整结构
```python
class ActionInfo:
    def __init__(self, user_query, search_thinking, search_query_list, search_result_info_list):
        self.user_query: str                                    # 用户查询
        self.search_thinking: str                               # 搜索思考过程
        self.search_query_list: List[str]                       # 搜索查询列表
        self.search_result_info_list: List[SearchResultInfo]    # 搜索结果信息列表
        self.sub_action_info_list: List[SubActionInfo] = []     # 子动作信息列表
```

### 4. 上下文存储阶段

#### 存储到 Handler 上下文
```python
# 线程安全的上下文存储
if idx not in self.id_to_context:
    with self.id_to_context_lock:
        self.id_to_context[idx] = []

with self.id_to_context_lock:
    self.id_to_context[idx].append(cur_action_info)
```

#### 上下文结构
```python
# Handler.id_to_context 的结构
{
    idx1: [ActionInfo, ActionInfo, ...],  # 会话1的动作历史
    idx2: [ActionInfo, ActionInfo, ...],  # 会话2的动作历史
    # ...
}
```

### 5. 返回给 Rollout 阶段

#### 格式化返回数据
```python
content = []
for search_result_info in search_result_info_list:
    search_query = search_result_info.search_query
    ret_web_page_info_list = []
    for web_page_info in search_result_info.web_page_info_list:
        ret_web_page_info_list.append({
            "title": web_page_info.title,
            "url": web_page_info.url,
            "quick_summary": web_page_info.quick_summary
        })
    content.append({
        "search_query": search_query,
        "web_page_info_list": ret_web_page_info_list
    })

return content  # 返回给调用方
```

#### 最终返回结构
```python
[
    {
        "search_query": "Python programming features",
        "web_page_info_list": [
            {
                "title": "Python Features",
                "url": "https://example1.com",
                "quick_summary": "Python is..."
            },
            {
                "title": "Why Python",
                "url": "https://example2.com", 
                "quick_summary": "Python offers..."
            }
        ]
    },
    {
        "search_query": "Python language characteristics",
        "web_page_info_list": [
            {
                "title": "Python Characteristics",
                "url": "https://example3.com",
                "quick_summary": "Key features..."
            }
        ]
    }
]
```

## 关键数据结构关系

### 层次结构
```
ActionInfo (顶层容器)
├── user_query: str
├── search_thinking: str  
├── search_query_list: List[str]
├── search_result_info_list: List[SearchResultInfo]
│   └── SearchResultInfo
│       ├── search_query: str
│       ├── web_page_info_list: List[WebPageInfo]
│       │   └── WebPageInfo
│       │       ├── title: str
│       │       ├── url: str
│       │       ├── quick_summary: str
│       │       ├── sub_question: str
│       │       ├── browser: SimpleTextBrowser | None
│       │       └── page_read_info_list: List[PageReadInfo]
│       └── web_select_info_list: List[WebSelectInfo]
└── sub_action_info_list: List[SubActionInfo]
```

## 后续处理流程

### 网页浏览阶段
当用户选择浏览特定网页时：

1. **从上下文获取数据**
```python
action_info = self.id_to_context[idx][-1]  # 获取最新的ActionInfo
search_result_info_list = action_info.search_result_info_list
```

2. **调用阅读代理**
```python
read_webpage_list = self.reading_agent.read_batch(
    user_query=question,
    search_result_info_list=search_result_info_list,
    url_list=url_list,
    web_search_agent=self.web_search_agent
)
```

3. **填充详细信息**
- `WebPageInfo.browser` 被填充为 `SimpleTextBrowser` 对象
- `WebPageInfo.page_read_info_list` 被填充为 `PageReadInfo` 列表

## 重要特性

### 1. 线程安全
- 使用 `threading.Lock()` 保护共享数据结构
- 确保并发访问时的数据一致性

### 2. 状态管理
- 通过 `idx` 区分不同的会话/rollout
- 维护每个会话的完整动作历史

### 3. 数据持久化
- ActionInfo 支持 `to_dict()` 和 `from_dict()` 序列化
- 可以保存和恢复完整的搜索状态

### 4. 渐进式数据填充
- 初始阶段：基本搜索结果
- 浏览阶段：填充 browser 对象
- 阅读阶段：填充 page_read_info_list

## 总结

WebSearchAgent 的结果通过以下关键步骤加入到 rollout 中：

1. **搜索执行** → `List[List[WebPageInfo]]`
2. **结构化包装** → `List[SearchResultInfo]`  
3. **动作封装** → `ActionInfo`
4. **上下文存储** → `Handler.id_to_context`
5. **格式化返回** → 简化的字典结构

这个设计确保了搜索结果能够被有效地存储、管理和在后续的 rollout 步骤中使用，同时保持了数据的完整性和可追溯性。
