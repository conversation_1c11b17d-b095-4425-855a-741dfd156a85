# -------------------------------
# build-system
# -------------------------------
[build-system]
requires = [
    "setuptools>=61.0",
    "wheel"
]
build-backend = "setuptools.build_meta"

# -------------------------------
# project (PEP 621 metadata)
# -------------------------------
[project]
name = "verl"
# We'll mark the version as "dynamic" because it's read from the file "verl/version/version" 
# (PEP 621 calls this "dynamic version"). 
# The actual version is specified in the [tool.setuptools.dynamic] section below.
dynamic = ["version"]

description = "verl: Volcano Engine Reinforcement Learning for LLM"
license = {file = "LICENSE"}  # or "Apache-2.0", if you prefer an SPDX identifier
readme = {file = "README.md", content-type = "text/markdown"}
requires-python = ">=3.8"

authors = [
  { name = "Bytedance - Seed - MLSys", email = "<EMAIL>" },
  { name = "Bytedance - Seed - MLSys", email = "<EMAIL>" },
]

# Dependencies corresponding to install_requires in setup.py
dependencies = [
    "accelerate",
    "codetiming",
    "datasets",
    "dill",
    "hydra-core",
    "numpy",
    "pandas",
    "peft",
    "pyarrow>=15.0.0",
    "pybind11",
    "pylatexenc",
    "ray>=2.10",
    "tensordict<0.6",
    "torchdata",
    "transformers",
    "vllm<=0.6.3",
    'wandb',
]

# Optional dependencies (extras_require in setup.py)
[project.optional-dependencies]
test = [
  "pytest", "yapf", "py-spy",
]
prime = ["pyext"]
gpu = ["liger-kernel", "flash-attn"]

# URLs
[project.urls]
Homepage = "https://github.com/volcengine/verl"

# -------------------------------
# tool.setuptools - Additional config
# -------------------------------
[tool.setuptools]
# True means `setuptools` will attempt to include all relevant files in package_data automatically.
# This corresponds to `include_package_data=True` in setup.py.
include-package-data = true

# We read the version from a file in 'verl/version/version'
[tool.setuptools.dynamic]
version = {file = "verl/version/version"}

# If you need to mimic `package_dir={'': '.'}`:
[tool.setuptools.package-dir]
"" = "."

# If you need to include specific non-Python data (like YAML files or version file):
# This is the rough equivalent of package_data={'': ['version/*'], 'verl': ['trainer/config/*.yaml']}
[tool.setuptools.package-data]
verl = [
  "version/*",
  "trainer/config/*.yaml"
]
