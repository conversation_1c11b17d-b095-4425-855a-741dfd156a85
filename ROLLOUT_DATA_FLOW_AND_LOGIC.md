# Rollout 数据流和逻辑详解

## 概述

Rollout 是强化学习训练中的关键组件，负责生成序列、执行工具调用、管理对话状态，并为后续的训练提供数据。本文档详细分析了系统中 rollout 的数据流转和执行逻辑。

## 整体架构

```
DataProto (输入) → LLMGenerationManager → Actor Rollout → WebSearchAgent → 更新状态 → DataProto (输出)
```

## 核心组件

### 1. LLMGenerationManager
- **职责**: 管理整个生成流程，协调各个组件
- **输入**: `DataProto` 包含初始 prompts
- **输出**: 完整的对话轨迹和最终的 `DataProto`

### 2. Actor Rollout Worker Group
- **职责**: 执行实际的序列生成
- **类型**: HFRollout, vLLMRollout, NaiveRollout
- **功能**: 基于当前状态生成下一个 token 序列

### 3. WebSearchAgent Integration
- **职责**: 处理工具调用，执行搜索和网页浏览
- **数据流**: 通过文件系统进行进程间通信

## 详细数据流

### 阶段 1: 初始化和准备

#### 输入数据结构
```python
gen_batch: DataProto = {
    'batch': {
        'input_ids': torch.Tensor,      # 初始问题的 token ids
        'attention_mask': torch.Tensor,  # 注意力掩码
        'position_ids': torch.Tensor     # 位置编码
    },
    'meta_info': {
        'eos_token_id': int,
        'pad_token_id': int,
        # ... 其他元信息
    }
}
```

#### 初始化过程
```python
# 1. 解析问题
query_contents = self.parse_question(gen_batch.batch['input_ids'])

# 2. 构建消息列表
messages_list = []
for idx, query_content in enumerate(query_contents):
    for _ in range(self.config.n):  # n 是每个问题的采样数
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": query_content}
        ]
        messages_list.append(messages)
batch_size(q) * n(messages_list)
# 3. 初始化状态
activate_list = [i for i in range(len(messages_list))]  # 活跃的对话索引
message_string_list = ["" for _ in range(len(messages_list))]  # 完整对话字符串
```

### 阶段 2: 多轮对话循环

#### 主循环结构
```python
for step in range(self.config.max_turns):
    # 2.1 准备当前轮输入
    activate_messages_list = [messages_list[i] for i in activate_list]
    
    # 2.2 构建 prompt
    rollings_active = self.tokenizer.apply_chat_template(
        activate_messages_list, 
        add_generation_prompt=True, 
        tools=self.tools, 
        tokenize=False
    )
    
    ### msj todo: 1、如果不强制加入think的效果呢？2、如果先检索并理解问题背景知识，然后再检索问题的答案，效果会如何。

    # 2.3 添加思考标签
    rollings_active = [rolling + "<think>" for rolling in rollings_active]
    
    # 2.4 tokenize 和 padding
    rollings_active = self.tokenizer(rollings_active, return_tensors="pt", padding=True)
    
    # 2.5 创建 DataProto
    rollings_active = DataProto.from_dict({
        'input_ids': rollings_active['input_ids'],
        'attention_mask': rollings_active['attention_mask'],
        'position_ids': rollings_active['position_ids'],
    })
    
    # 2.6 生成序列
    gen_output = self._generate_with_gpu_padding(rollings_active)
    
    # 2.7 解析响应
    results = self.parse_response(gen_output.batch['responses'], think=True)
    
    # 2.8 处理结果
    # ... (详见下文)
```

### 阶段 3: 序列生成

#### Actor Rollout 调用
```python
def _generate_with_gpu_padding(self, active_batch: DataProto) -> DataProto:
    # 处理多GPU填充
    num_gpus = self.config.num_gpus * self.config.nnodes
    if num_gpus > 1:
        # 填充到GPU数量的倍数
        # ...
    
    # 调用实际的生成器
    return self.actor_rollout_wg.generate_sequences(active_batch)
```

#### vLLM Rollout 生成过程
```python
def generate_sequences(self, prompts: DataProto) -> DataProto:
    # 1. 提取输入
    idx = prompts.batch['input_ids']
    attention_mask = prompts.batch['attention_mask']
    position_ids = prompts.batch['position_ids']
    
    # 2. 转换为 vLLM 格式
    idx_list = []
    for i in range(batch_size):
        idx_list.append(_pre_process_inputs(self.pad_token_id, idx[i]))
    
    # 3. 生成
    output = self.inference_engine.generate(
        prompts=None,
        sampling_params=self.sampling_params,
        prompt_token_ids=idx_list,
        use_tqdm=False
    )
    
    # 4. 后处理
    response = []
    for output in outputs:
        response.append(output.outputs[0].token_ids)
    
    # 5. 构建返回结果
    return DataProto(batch={
        'prompts': idx,
        'responses': response,
        'input_ids': seq,
        'attention_mask': attention_mask,
        'position_ids': position_ids
    })
```

### 阶段 4: 响应解析和分类

#### 解析逻辑
```python
def parse_response(self, input_ids: torch.Tensor, think: bool = False) -> List[Tuple[bool, str, str]]:
    """
    返回: [(is_stop, thinking, answer/tool_call), ...]
    is_stop: True 表示对话结束，False 表示需要工具调用
    """
    response_contents = self.tokenizer.batch_decode(input_ids)
    results = []
    
    for content in response_contents:
        if "<think>" in content and "<answer>" in content:
            # 最终答案
            think = content.split("<think>")[1].split("</think>")[0]
            answer = content.split("<answer>")[1].split("</answer>")[0]
            results.append((True, think, answer))
            
        elif "<think>" in content and "<tool_call>" in content:
            # 工具调用
            think = content.split("<think>")[1].split("</think>")[0]
            tool_call = content.split("<tool_call>")[1].split("</tool_call>")[0]
            tool_call = json.loads(tool_call)  # 验证格式
            results.append((False, think, tool_call))
            
        else:
            # 格式错误，标记为结束
            results.append((True, "", ""))
    
    return results
```

### 阶段 5: 工具调用执行

#### 工具调用流程
```python
# 1. 分离完成和继续的对话
activate_list_copy = []
tool_call_list = []

for i in range(len(results)):
    if results[i][0]:  # 对话结束
        # 保存完整对话
        message_string_list[activate_list[i]] = full_conversation
    else:  # 需要工具调用
        activate_list_copy.append(activate_list[i])
        tool_call_list.append((
            activate_list[i],                           # 对话索引
            messages_list[activate_list[i]][1]["content"],  # 原始问题
            results[i][1],                              # 思考过程
            results[i][2]                               # 工具调用
        ))

# 2. 执行工具调用
tool_call_list = self.execute_predictions(tool_call_list, len(messages_list))
```

#### 进程间通信机制
```python
def execute_predictions(self, tool_call_list: List[Tuple], total_number: int):
    # 1. 准备数据
    query_contents = [{
        "idx": tool_call[0],
        "question": tool_call[1], 
        "think": tool_call[2],
        "tool_call": tool_call[3],
        "total_number": total_number
    } for tool_call in tool_call_list]
    
    # 2. 写入数据文件
    with open(self.config.data_writing_file, 'w', encoding='utf-8') as f:
        json.dump(query_contents, f, indent=4, ensure_ascii=False)
    
    # 3. 发送信号
    with open(self.config.signal_writing_file, 'w', encoding='utf-8') as f:
        json.dump({'signal': self.config.QUERY_SIGNAL}, f)
    
    # 4. 等待响应
    while True:
        with open(self.config.signal_writing_file, 'r') as f:
            signal_contents = json.load(f)
        if signal_contents['signal'] == self.config.RESPONSE_SIGNAL:
            break
        time.sleep(10)
    
    # 5. 读取结果
    with open(self.config.data_writing_file, 'r', encoding='utf-8') as f:
        query_contents = json.load(f)
    
    return query_contents
```

### 阶段 6: 状态更新

#### 消息历史更新
```python
for i in range(len(tool_call_list)):
    # 添加助手消息（包含工具调用）
    messages_list[tool_call_list[i]['idx']].append({
        "role": "assistant", 
        "content": "<think>" + tool_call_list[i]['think'] + "</think>", 
        "tool_calls": [{
            "type": "function", 
            "function": tool_call_list[i]['tool_call']
        }]
    })
    
    # 添加工具响应消息
    messages_list[tool_call_list[i]['idx']].append({
        "role": "tool", 
        "name": tool_call_list[i]['tool_call']["name"],
        "content": tool_call_list[i]['content']  # WebSearchAgent 的返回结果
    })

# 更新活跃列表
activate_list = activate_list_copy
```

### 阶段 7: 最终输出构建

#### 数据整理和输出
```python
# 1. 处理未完成的对话
if activate_list != []:
    for i in activate_list:
        message_string_list[i] = self.tokenizer.apply_chat_template(
            messages_list[i], 
            add_generation_prompt=True, 
            tools=self.tools, 
            tokenize=False
        )

# 2. 分离 prompt 和 response
response_str_list = []
initial_prompt_list = []
for i, messages in enumerate(messages_list):
    initial_prompt = self.tokenizer.apply_chat_template(
        messages[0:2],  # system + user
        add_generation_prompt=True, 
        tools=self.tools, 
        tokenize=False
    )
    initial_prompt_list.append(initial_prompt)
    response_str_list.append(message_string_list[i][len(initial_prompt):])

# 3. 构建最终 DataProto
message_tensor = DataProto.from_dict({
    'prompts': prompts_repeated,
    'responses': responses,
    'input_ids': torch.cat((prompts_repeated, responses), dim=-1),
    'attention_mask': attention_mask,
    'position_ids': position_ids,
})
message_tensor.meta_info.update(meta_info)
message_tensor.non_tensor_batch['agent_grpo_idx'] = np.array(agent_grpo_idx, dtype=object)
```

## 关键数据结构

### DataProto
```python
class DataProto:
    batch: Dict[str, torch.Tensor]      # 主要的张量数据
    meta_info: Dict[str, Any]           # 元信息
    non_tensor_batch: Dict[str, Any]    # 非张量数据
```

### 消息格式
```python
messages = [
    {"role": "system", "content": "系统提示"},
    {"role": "user", "content": "用户问题"},
    {"role": "assistant", "content": "助手回复", "tool_calls": [...]},
    {"role": "tool", "name": "工具名", "content": "工具结果"},
    # ... 更多轮次
]
```

## 状态管理

### 活跃对话跟踪
- `activate_list`: 当前仍在进行的对话索引
- `message_string_list`: 每个对话的完整字符串表示
- `messages_list`: 每个对话的结构化消息历史

### 并行处理
- 支持多个对话同时进行
- 每轮只处理活跃的对话
- 完成的对话从活跃列表中移除

## 输出和持久化

### 中间结果保存
```python
# 每轮保存状态
f"./outputs/{project_name}/{experiment_name}/rollout/rollout_step_{global_steps}_round_{step}.json"

# 最终结果保存  
f"./outputs/{project_name}/{experiment_name}/rollout/rollout_step_{global_steps}.json"
```

### 返回值
```python
return message_string_list, message_tensor
```

## 总结

Rollout 的数据流是一个复杂的多轮对话管理系统，它：

1. **管理状态**: 跟踪多个并行对话的状态
2. **生成序列**: 使用 Actor 模型生成响应
3. **执行工具**: 与 WebSearchAgent 协作执行搜索
4. **更新历史**: 维护完整的对话历史
5. **构建输出**: 为训练准备结构化数据

这个设计支持了复杂的多轮对话场景，并为强化学习训练提供了丰富的轨迹数据。
