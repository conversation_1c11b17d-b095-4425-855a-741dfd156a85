import asyncio
import re
from hashlib import md5
from typing import Any, Callable, Awaitable, Optional


class UnlimitedSemaphore:
    async def __aenter__(self):
        pass

    async def __aexit__(self, exc_type, exc, tb):
        pass


def clean_str(input: Any) -> str:
    if not isinstance(input, str):
        return input
    result = input.strip()
    return re.sub(r"[\x00-\x1f\x7f-\x9f]", "", result)


def is_float_regex(value: str) -> bool:
    return bool(re.match(r"^[-+]?[0-9]*\.?[0-9]+$", value))


def split_string_by_multi_markers(content: str, markers: list[str]) -> list[str]:
    if not markers:
        return [content]
    results = re.split("|".join(re.escape(marker) for marker in markers), content)
    return [r.strip() for r in results if r.strip()]


def compute_mdhash_id(content: str, prefix: str = "") -> str:
    return prefix + md5(content.encode()).hexdigest()


def compute_args_hash(*args) -> str:
    return md5(str(args).encode()).hexdigest()


def limit_async_func_call(max_size: int, waitting_time: float = 0.0001):
    def final_decro(func: Callable[..., Awaitable]):
        __current_size = 0

        async def wait_func(*args, **kwargs):
            nonlocal __current_size
            while __current_size >= max_size:
                await asyncio.sleep(waitting_time)
            __current_size += 1
            try:
                result = await func(*args, **kwargs)
            finally:
                __current_size -= 1
            return result

        return wait_func

    return final_decro


async def cache_lookup(hashing_kv, mode: str, args_hash: str) -> Optional[str]:
    if hashing_kv is None:
        return None
    data = await hashing_kv.get_by_id(mode) or {}
    entry = data.get(args_hash)
    if not entry:
        return None
    return entry.get("return")


async def cache_save(hashing_kv, mode: str, args_hash: str, content: str, prompt: str = ""):
    if hashing_kv is None:
        return
    data = await hashing_kv.get_by_id(mode) or {}
    data[args_hash] = {"return": content, "prompt": prompt}
    await hashing_kv.upsert({mode: data})

