import asyncio
from graphr1.hypergraph import (
    aextract_from_raw_texts_plus,
    kg_query,
    ChunkConfig,
    QueryParam,
)
from graphr1.hypergraph.adapters.memory import (
    MemoryGraphStorage,
    MemoryVectorStorage,
    MemoryKVStorage,
)


async def mock_llm_func(prompt: str, max_tokens: int | None = None, history_messages: list[dict] | None = None) -> str:
    """
    A trivial LLM mock that returns one hyper-relation and two entities in the expected format.
    It ignores the prompt and history, and just returns constant extraction output.
    """
    tuple_delim = "<|>"
    record_delim = "##"
    completion = "<|COMPLETE|>"
    # Example: <PERSON> met <PERSON> at the park.
    records = [
        f'("hyper-relation"{tuple_delim}"Alice met <PERSON> at the park."{tuple_delim}8)',
        f'("entity"{tuple_delim}"Alice"{tuple_delim}"person"{tuple_delim}"Alice is a person"{tuple_delim}95)',
        f'("entity"{tuple_delim}"Bob"{tuple_delim}"person"{tuple_delim}"<PERSON> is a person"{tuple_delim}90)',
    ]
    return record_delim.join(records) + record_delim + completion


async def main():
    # Prepare storages
    graph = MemoryGraphStorage()
    entity_vdb = MemoryVectorStorage()
    hyperedge_vdb = MemoryVectorStorage()
    cache_kv = MemoryKVStorage()

    # Raw docs
    docs = [
        {"id": "doc1", "content": "Alice met Bob at the park."},
    ]

    # Optional chunking
    chunk_cfg = ChunkConfig(enabled=True, max_token_size=256, overlap_token_size=32)

    # Run end-to-end extraction (with cache + summary enabled inside)
    await aextract_from_raw_texts_plus(
        docs,
        knowledge_graph_inst=graph,
        entity_vdb=entity_vdb,
        hyperedge_vdb=hyperedge_vdb,
        llm_func=mock_llm_func,
        language="English",
        entity_extract_max_gleaning=1,
        chunk_config=chunk_cfg,
        hashing_kv=cache_kv,
    )

    # Query example: provide candidate entity and hyperedge ids
    entities_ids = ['"ALICE"', '"BOB"']
    hyperedges_ids = ['<hyperedge>Alice met Bob at the park.']

    param = QueryParam(top_k=5)
    knowledge = await kg_query(
        query="Alice and Bob",
        knowledge_graph_inst=graph,
        entities_vdb=entities_ids,
        hyperedges_vdb=hyperedges_ids,
        text_chunks_db=MemoryKVStorage(),
        query_param=param,
    )

    print("Knowledge:", knowledge)


if __name__ == "__main__":
    # This script is for illustration only; do not run in restricted environments.
    asyncio.run(main())

