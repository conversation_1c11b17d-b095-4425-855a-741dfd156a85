from typing import List, Dict, Any
import requests
import json
import logging
from .prompts import *
import concurrent.futures
from .search.text_web_browser import <PERSON>TextBrowser
from utils import *
from webpage import WebPageInfo
from .search.search_api import web_search
import threading
import os
import time
import random
import copy
from datetime import datetime
from time import strftime, gmtime
import typing as tp
from bs4 import BeautifulSoup
import warnings
import functools
import sqlite3

flag = True

logging.basicConfig(
    level=logging.INFO,  # 设置日志级别
    format='%(asctime)s - %(levelname)s - %(message)s'  # 设置日志格式
)

def deprecated(reason):
    """
    装饰器，用于标记废弃的方法

    Args:
        reason (str): 废弃原因
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            warnings.warn(
                f"{func.__name__} is deprecated: {reason}",
                DeprecationWarning,
                stacklevel=2
            )
            return func(*args, **kwargs)
        return wrapper
    return decorator

class WikiAgent:
    def __init__(self, client, config):
        self.client = client
        self.config = config
        self.cache_file = "/home/<USER>/code/personal/S9059385/MTR-main/wiki_cache.db"  # 缓存文件路径
        self.conn = sqlite3.connect(self.cache_file, check_same_thread=False)
        self._initialize_cache()

    def _initialize_cache(self):
        """初始化缓存表"""
        with self.conn:
            self.conn.execute(
                """
                CREATE TABLE IF NOT EXISTS cache (
                    url TEXT PRIMARY KEY,
                    content TEXT
                )
                """
            )

    def _load_cache(self, url):
        """从数据库加载缓存"""
        cursor = self.conn.execute("SELECT content FROM cache WHERE url = ?", (url,))
        row = cursor.fetchone()
        return row[0] if row else None

    def _save_cache(self, url, content):
        """将缓存保存到数据库"""
        with self.conn:
            self.conn.execute("INSERT OR REPLACE INTO cache (url, content) VALUES (?, ?)", (url, content))

    def get_raw_wikipedia_content(self, entity_name: str, alias_names: List[str], include_full_content: bool = True, return_as_sections_list: bool = False):
        """
        获取Wikipedia页面内容，支持按section划分

        Args:
            entity_name (str): 实体名称
            alias_names (list): 别名列表
            include_full_content (bool): 是否包含完整内容（包括所有sections），默认False只返回摘要
            return_as_sections_list (bool): 是否返回section列表格式，默认False返回字符串

        Returns:
            str or list: Wikipedia页面内容，根据参数返回字符串或section列表
        """
        wikipedia_url_base = 'https://en.wikipedia.org/wiki/{}'
        names_to_try = [entity_name] + (alias_names if (alias_names and len(alias_names) > 0) else [])

        for name in names_to_try:
            if not name:
                continue
            name = name.strip().replace(" ", "_")
            wikipedia_url = wikipedia_url_base.format(name)
            print('wikipedia_url  ' + wikipedia_url)

            # 检查缓存
            cached_content = self._load_cache(wikipedia_url)
            if cached_content:
                print(f"Cache hit for URL: {wikipedia_url}")
                return cached_content

            try:
                response = requests.get(wikipedia_url, headers={'Connection': 'close'}, timeout=180)
                if response.status_code == 404:
                    continue  # Try next alias
                response.raise_for_status()

                soup = BeautifulSoup(response.content, "html.parser")

                # 查找主要内容区域
                content_div = soup.find("div", {"id": "mw-content-text"})
                if not content_div:
                    content_div = soup.find("div", {"id": "bodyContent"})
                if not content_div:
                    continue  # Try next alias

                # 移除不需要的元素
                for unwanted in content_div.find_all(["script", "style", "table", "div"],
                                                   class_=["navbox", "infobox", "metadata", "ambox", "hatnote"]):
                    unwanted.decompose()

                # 移除引用和脚注
                for ref in content_div.find_all(["sup", "span"], class_=["reference", "mw-ref"]):
                    ref.decompose()

                if return_as_sections_list and include_full_content:
                    content = self._extract_full_content_as_sections_list(content_div)
                elif include_full_content:
                    content = self._extract_full_content_with_sections(content_div)
                else:
                    content = self._extract_summary_content(content_div)

                # 更新缓存
                self._save_cache(wikipedia_url, content)
                return content

            except requests.exceptions.RequestException as e:
                print(f"Error fetching Wikipedia page for '{name}': {e}")
                continue  # Try next alias

        return "Not Found!" if not return_as_sections_list else []

    def _extract_summary_content(self, content_div):
        """提取摘要内容（第一个h2标签之前的内容）"""
        summary_content = ""

        # 查找第一个段落容器
        parser_output = content_div.find("div", class_="mw-parser-output")
        if parser_output:
            content_div = parser_output

        for element in content_div.children:
            if hasattr(element, 'name'):
                if element.name == "h2":
                    break
                elif element.name in ["p", "div"]:
                    text = element.get_text().strip()
                    if text and len(text) > 10:  # 过滤掉太短的文本
                        summary_content += text + "\n\n"

        return summary_content.strip()

    def _extract_full_content_with_sections(self, content_div):
        """提取完整内容，按section组织，返回格式化的字符串"""
        content_sections = []
        current_section = {"title": "Summary", "content": ""}

        # 查找第一个段落容器
        parser_output = content_div.find("div", class_="mw-parser-output")
        if parser_output:
            content_div = parser_output

        for element in content_div.children:
            if hasattr(element, 'name'):
                if element.name in ["h2", "h3", "h4"]:
                    # 保存当前section
                    if current_section["content"].strip():
                        content_sections.append(current_section)

                    # 开始新section
                    section_title = element.get_text().strip()
                    # 移除编辑链接
                    section_title = section_title.replace("[edit]", "").strip()
                    current_section = {"title": section_title, "content": ""}

                elif element.name in ["p", "div", "ul", "ol"]:
                    text = element.get_text().strip()
                    if text and len(text) > 10:  # 过滤掉太短的文本
                        current_section["content"] += text + "\n\n"

        # 添加最后一个section
        if current_section["content"].strip():
            content_sections.append(current_section)

        # 格式化输出
        formatted_content = ""
        for section in content_sections:
            if section["title"] == "Summary":
                formatted_content += section["content"] + "\n"
            else:
                formatted_content += f"\n## {section['title']}\n\n{section['content']}"

        return formatted_content.strip()

    def _extract_full_content_as_sections_list(self, content_div):
        """提取完整内容，返回section列表，格式为<SECTION_NAME>:<SECTION_CONTENT>"""
        content_sections = []
        current_section = {"title": "Summary", "content": ""}

        # 查找第一个段落容器
        parser_output = content_div.find("div", class_="mw-parser-output")
        if parser_output:
            content_div = parser_output

        for element in content_div.children:
            if hasattr(element, 'name'):
                if element.name in ["h2", "h3", "h4"]:
                    # 保存当前section
                    if current_section["content"].strip():
                        content_sections.append(current_section)

                    # 开始新section
                    section_title = element.get_text().strip()
                    # 移除编辑链接
                    section_title = section_title.replace("[edit]", "").strip()
                    current_section = {"title": section_title, "content": ""}

                elif element.name in ["p", "div", "ul", "ol"]:
                    text = element.get_text().strip()
                    if text and len(text) > 10:  # 过滤掉太短的文本
                        current_section["content"] += text + "\n\n"

        # 添加最后一个section
        if current_section["content"].strip():
            content_sections.append(current_section)

        # 格式化为<SECTION_NAME>:<SECTION_CONTENT>的列表
        formatted_sections = []
        for section in content_sections:
            section_content = section["content"].strip()
            if section_content:
                formatted_sections.append(f"<{section['title']}>:{section_content}")

        return formatted_sections
    def clean_content(self, content):
        """
        Clean Wikipedia page content by:
        1. Removing References section and everything after it (case-sensitive)
        2. Splitting by newlines and removing paragraphs shorter than 20 characters

        Args:
            content (str): Raw Wikipedia content to clean

        Returns:
            str: Cleaned content
        """
        if not content:
            return ""

        # Remove References section and everything after it (case-sensitive)
        references_index = content.find("References")
        if references_index != -1:
            content = content[:references_index]

        # Split by newlines and filter out short paragraphs
        paragraphs = content.split('\n')
        cleaned_paragraphs = []

        for paragraph in paragraphs:
            # Strip whitespace and check length
            cleaned_paragraph = paragraph.strip()
            if len(cleaned_paragraph) >= 20:
                cleaned_paragraphs.append(cleaned_paragraph)

        # Join the cleaned paragraphs back with newlines
        return '\n'.join(cleaned_paragraphs)

    def get_wikipedia_page(self, entity_name: str, alias_names: List[str] , search_query: str , user_query: str = None,include_full_content: bool = False,  extract_relevant_content: bool = False) -> str:
        """
        获取Wikipedia页面内容并进行清理，可选择使用LLM提取相关内容

        Args:
            entity_name (str): 实体名称
            alias_names (list): 别名列表，默认为None
            include_full_content (bool): 是否包含完整内容，默认False
            user_query (str): 用户查询，用于LLM内容提取，默认为None
            search_query (str): 搜索查询，用于LLM内容提取，默认为None
            extract_relevant_content (bool): 是否使用LLM提取相关内容，默认False

        Returns:
            str: 清理后的Wikipedia页面内容，失败时返回"Not Found!"
                 如果启用LLM提取且提取成功，返回LLM提取的相关内容
        """
        if alias_names is None:
            alias_names = []

        raw_content = self.get_raw_wikipedia_content(entity_name, alias_names, include_full_content)
        if raw_content == "Not Found!":
            return "The entity not exist. Please try other search."

        cleaned_content = self.clean_content(raw_content)

        # 如果启用LLM内容提取且提供了查询信息
        if extract_relevant_content and search_query and cleaned_content:
            try:
                extracted_content = self.extract_relevant_wikipedia_content(user_query, search_query, cleaned_content)
                if extracted_content:
                    return extracted_content
            except Exception as e:
                print(f"LLM content extraction failed: {e}")
                # 如果LLM提取失败，返回原始清理后的内容

        return "LLM for wikipage error"

    def extract_relevant_wikipedia_content(self, user_query: str, search_query: str, wikipedia_content: str) -> str:
        """
        使用LLM从Wikipedia内容中提取与用户查询相关的信息

        Args:
            user_query (str): 用户查询(丢)
            search_query (str): 搜索查询
            wikipedia_content (str): 清理后的Wikipedia内容

        Returns:
            str: LLM提取的相关内容，如果提取失败或不相关则返回空字符串
        """
        from .prompts import WIKIPEDIA_CONTENT_EXTRACTION_PROMPT

        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": WIKIPEDIA_CONTENT_EXTRACTION_PROMPT.format(
                search_query=search_query,
                wikipedia_content=wikipedia_content
            )},
        ]

        response = get_response_from_llm(
            messages=messages,
            client=self.client,
            model=self.config.get("quick_summary_model", "Qwen3-8B"),  # 使用与WebSearchAgent相同的模型
            stream=False,
        )["content"]
        global flag
        if flag:
            print(response)
            flag = False
        # 检查是否有帮助
        helpful = get_content_from_tag(response, "helpful", "no").strip().lower()
        if helpful == "yes":
            summary = get_content_from_tag(response, "summary", "").strip()
            return summary

        return "No info found."

    # @deprecated("This method is not used in the main business logic. Use get_raw_wikipedia_content() with return_as_sections_list=True instead.")
    # def get_wikipedia_sections_list(self, entity_name, alias_names=None):
    #     """
    #     获取Wikipedia页面内容，返回section列表格式：<SECTION_NAME>:<SECTION_CONTENT>

    #     .. deprecated::
    #         This method is deprecated and will be removed in a future version.
    #         Use get_raw_wikipedia_content() with return_as_sections_list=True instead.

    #     Args:
    #         entity_name (str): 实体名称
    #         alias_names (list): 别名列表，默认为None

    #     Returns:
    #         list: section列表，每个元素格式为"<SECTION_NAME>:<SECTION_CONTENT>"
    #     """
    #     if alias_names is None:
    #         alias_names = []

    #     sections_list = self.get_raw_wikipedia_content(
    #         entity_name, alias_names,
    #         include_full_content=True,
    #         return_as_sections_list=True
    #     )

    #     if not sections_list:  # 如果返回空列表（未找到）
    #         return []

    #     # 对每个section的内容进行清理
    #     cleaned_sections = []
    #     for section in sections_list:
    #         # 分离section名称和内容
    #         if ':' in section:
    #             section_name, section_content = section.split(':', 1)
    #             # 清理内容
    #             cleaned_content = self.clean_content(section_content)
    #             if cleaned_content.strip():  # 只保留有内容的section
    #                 cleaned_sections.append(f"{section_name}:{cleaned_content}")

    #     return cleaned_sections

    # @deprecated("This method is not used in the main business logic. Use get_wikipedia_page() or get_raw_wikipedia_content() instead.")
    # def search_wiki(self, entity_name, alias_names=None, return_sections_list=True):
    #     """
    #     搜索Wikipedia页面，返回格式化的结果

    #     .. deprecated::
    #         This method is deprecated and will be removed in a future version.
    #         Use get_wikipedia_page() or get_raw_wikipedia_content() instead.

    #     Args:
    #         entity_name (str): 实体名称
    #         alias_names (list): 别名列表，默认为None
    #         return_sections_list (bool): 是否返回section列表格式，默认False

    #     Returns:
    #         dict: 包含Wikipedia搜索结果的字典
    #     """
    #     if alias_names is None:
    #         alias_names = []

    #     if return_sections_list:
    #         # 返回section列表格式
    #         sections_list = self.get_wikipedia_sections_list(entity_name, alias_names)
    #         return {
    #             "entity": entity_name,
    #             "aliases": alias_names,
    #             "sections_list": sections_list,
    #             "status": "found" if sections_list else "not_found"
    #         }
    #     else:
    #         # 返回传统格式
    #         # 获取摘要内容
    #         summary_content = self.get_wikipedia_page(entity_name, alias_names, include_full_content=False)

    #         # 如果需要更详细的内容，可以获取完整内容
    #         full_content = None
    #         if summary_content != "Not Found!" and len(summary_content) < 500:
    #             # 如果摘要内容太短，尝试获取更多内容
    #             full_content = self.get_wikipedia_page(entity_name, alias_names, include_full_content=True)

    #         return {
    #             "entity": entity_name,
    #             "aliases": alias_names,
    #             "summary": summary_content,
    #             "full_content": full_content,
    #             "status": "found" if summary_content != "Not Found!" else "not_found"
    #         }

    # def get_wikipedia_page_batch(self, entity_name_list: List[str], alias_names_list: List[List[str]], include_full_content: bool = False, user_query_list: List[str] = None, search_query_list: List[str] = None, extract_relevant_content: bool = False) -> List[str]:
    #     """
    #     批量获取Wikipedia页面内容，支持LLM内容提取

    #     Args:
    #         entity_name_list (list): 实体名称列表
    #         alias_names_list (list): 对应的别名列表
    #         include_full_content (bool): 是否包含完整内容，默认False
    #         user_query_list (list): 用户查询列表，用于LLM内容提取，默认为None
    #         search_query_list (list): 搜索查询列表，用于LLM内容提取，默认为None
    #         extract_relevant_content (bool): 是否使用LLM提取相关内容，默认False

    #     Returns:
    #         list: Wikipedia页面内容列表

    #     """
    #     # 准备参数列表
    #     args_list = []
    #     for i, (entity_name, alias_names) in enumerate(zip(entity_name_list, alias_names_list)):
    #         user_query = user_query_list[i] if user_query_list and i < len(user_query_list) else None
    #         search_query = search_query_list[i] if search_query_list and i < len(search_query_list) else None

    #         args_list.append((
    #             entity_name,
    #             alias_names,
    #             include_full_content,
    #             user_query,
    #             search_query,
    #             extract_relevant_content
    #         ))

    #     with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:  # 降低并发数避免被限制
    #         future_to_content = [
    #             executor.submit(self.get_wikipedia_page, *args)
    #             for args in args_list
    #         ]

    #     wikipedia_page_list = []
    #     for future in future_to_content:
    #         wikipedia_page_list.append(future.result())
    #     return wikipedia_page_list
    


class WebSearchAgent:
    def __init__(self, client, config):
        self.client = client
        self.config = config
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"

        self.BROWSER_CONFIG = {
            "viewport_size": 1024 * 5 * 8,
            "downloads_folder": "downloads_folder",
            "request_kwargs": {
                "headers": {"User-Agent": self.user_agent},
                "timeout": (5, 10),
            },
            "serper_api_key": config['serper_api_key'],
        }
        downloads_folder_path = f"./{self.BROWSER_CONFIG['downloads_folder']}"
        if not os.path.exists(downloads_folder_path):
            logging.info(f"保存目录不存在，创建目录：{os.path.abspath(downloads_folder_path)}")
            os.makedirs(downloads_folder_path, exist_ok=True)
        
        self.search_history = {}
        self.search_history_lock = threading.Lock()
        self.url_browser_dict = {}
        self.url_browser_dict_lock = threading.Lock()
    
    def save(self):
        with open(self.config.query_save_path, 'w', encoding='utf-8') as f:
            json.dump(self.search_history, f, indent=4, ensure_ascii=False)
        print("保存完毕")
    
    def browser_to_json(self, browser: SimpleTextBrowser, title: str, url: str):
        page_list = []
        total_pages = len(browser.viewport_pages)
        while browser.viewport_current_page < total_pages:
            cur_web_page_content = browser._state()[1]
            page_list.append(cur_web_page_content)
            browser.page_down()
        # 提取browser的内容，取成一个json，然后保存到数据库
        return {
            "title": title,
            "url": url,
            "page_list": page_list
        }
    
    def scrape(self, browser, url: str) -> str:
        """爬取网页并使用LLM总结内容"""
        browser.visit_page(url)
        header, content = browser._state()
        return header.strip() + "\n=======================\n" + content
    
    def get_quick_summary(self, user_query, search_query, first_page_fetch_res):
        messages=[
            {"role": "system", "content": "Your are a helpful assistant."},
            {"role": "user", "content": QUICK_SUMMARY_PROMPT.format(
                user_query=user_query,
                search_query=search_query,
                first_page_fetch_res=first_page_fetch_res
            )},
        ]
        response = get_response_from_llm(
            messages=messages, 
            client=self.client,
            model=self.config["quick_summary_model"],
            stream=False,
        )["content"]
        
        helpful = True
        summary = get_content_from_tag(response, "summary", response).strip()
        return helpful, summary
    
    def is_error_page(self, browser: SimpleTextBrowser) -> bool:
        if isinstance(browser.page_title, tuple):
            return True
        return (browser.page_title is not None and 
                browser.page_title.startswith("Error ") and 
                browser.page_content is not None and 
                browser.page_content.startswith("## Error "))

    def fetch_content(self, browser: SimpleTextBrowser, url: str):
        try:
            return self.scrape(browser, url)
        except Exception as e:
            # logging.error(e)
            return "## Error : No valid information in this page"
    
    def scrape_and_check_valid(self, web_info: dict, user_query, search_query):
        quick_summary = web_info['snippet'] if 'snippet' in web_info else ""
        web_info['quick_summary'] = quick_summary
        if web_info['link'] in self.url_browser_dict:
            return copy.deepcopy(self.url_browser_dict[web_info['link']])
        browser = SimpleTextBrowser(**self.BROWSER_CONFIG)
        content = self.fetch_content(browser, web_info['link'])
        if content is None:
            return None
        
        if self.is_error_page(browser):
            logging.info(f"访问错误，抛弃URL：{web_info['link']}")
            return None
        
        with self.url_browser_dict_lock:
            self.url_browser_dict[web_info['link']] = copy.deepcopy(browser)
        
        return browser

    def scrape_and_check_valid_api(self, url):
        browser = SimpleTextBrowser(**self.BROWSER_CONFIG)
        content = self.fetch_content(browser, url)
        if content is None:
            return None
        
        if self.is_error_page(browser):
            logging.info(f"访问错误，抛弃URL：{url}")
            return None
        return browser
    
    def web_search_wrapper(self, search_query):
        if search_query in self.search_history and (time.time() - self.search_history[search_query]['timestamp'] <= 60 * 60 * 24):
            return self.search_history[search_query]['organic']
        organic = web_search(search_query, self.config)
        if len(organic) > 0:
            with self.search_history_lock:
                self.search_history[search_query] = {
                    "timestamp": time.time(),
                    "organic": organic
                }
        return organic

    def search_web(self, user_query, search_query: str, api_result_dict: dict) -> List[WebPageInfo]:
        organic = api_result_dict[search_query]['organic']
        web_info_list = []
        for site in organic:
            web_info_list.append(site)
        web_page_info_list = []
        for web_info in web_info_list:
            web_page_info_list.append(WebPageInfo(
                title=web_info['title'],
                url=web_info['link'],
                quick_summary=web_info['snippet'] if 'snippet' in web_info else "",
                browser=None,
                sub_question=search_query
            ))
        return web_page_info_list


    def search_web_batch(self, user_query: str, search_query_list: List[str], api_result_dict:dict) -> List[List[WebPageInfo]]:
        web_page_info_list_batch = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
            future_to_content = [executor.submit(self.search_web, user_query, search_query, api_result_dict) for search_query in search_query_list]
        for i, future in enumerate(future_to_content):
            web_page_info_list = future.result()
            web_page_info_list_batch.append(web_page_info_list)
        return web_page_info_list_batch
    
if __name__ == "__main__":
    w = WikiAgent(client=None, config=None)
    rst = w.get_wikipedia_page("Hydrochloride")
    print(rst)