# search_web_batch 输出结构详解

## 方法签名
```python
def search_web_batch(self, user_query: str, search_query_list: List[str], api_result_dict: dict) -> List[List[WebPageInfo]]
```

## 返回值类型
```python
List[List[WebPageInfo]]
```

## 完整嵌套结构

### 第一层：批量搜索结果列表
```python
[
    # 第一个搜索查询的结果
    [WebPageInfo, WebPageInfo, ...],
    # 第二个搜索查询的结果  
    [WebPageInfo, WebPageInfo, ...],
    # 第三个搜索查询的结果
    [WebPageInfo, WebPageInfo, ...],
    # ... 更多搜索查询结果
]
```

### 第二层：单个搜索查询的网页信息列表
```python
[
    WebPageInfo,  # 第一个搜索结果网页
    WebPageInfo,  # 第二个搜索结果网页
    WebPageInfo,  # 第三个搜索结果网页
    # ... 更多网页
]
```

### 第三层：WebPageInfo 对象详细结构

#### WebPageInfo 类属性
```python
class WebPageInfo:
    def __init__(self, title: str, url: str, quick_summary: str, sub_question, browser: SimpleTextBrowser = None):
        self.title: str                              # 网页标题
        self.url: str                                # 网页URL
        self.quick_summary: str                      # 快速摘要（来自搜索结果snippet）
        self.browser: SimpleTextBrowser | None       # 浏览器对象（初始为None）
        self.sub_question: str                       # 子问题（即搜索查询）
        self.page_read_info_list: List[PageReadInfo] # 页面阅读信息列表（初始为空）
```

#### WebPageInfo.browser (SimpleTextBrowser) 详细结构
```python
class SimpleTextBrowser:
    # 基本属性
    start_page: str                                  # 起始页面URL
    viewport_size: int                               # 视口大小（字符数）
    downloads_folder: str | None                     # 下载文件夹路径
    
    # 页面状态
    page_title: str | None                           # 当前页面标题
    viewport_current_page: int                       # 当前视口页面索引
    viewport_pages: List[Tuple[int, int]]           # 视口页面边界列表 [(开始位置, 结束位置), ...]
    history: List[Tuple[str, float]]                # 浏览历史 [(URL, 时间戳), ...]
    
    # 页面内容
    _page_content: str                               # 完整页面内容
    
    # 搜索相关
    _find_on_page_query: str | None                 # 当前搜索查询
    _find_on_page_last_result: int | None           # 最后搜索结果位置
    
    # API配置
    serpapi_key: str | None                         # SerpAPI密钥
    serper_api_key: str | None                      # Serper API密钥
    request_kwargs: Dict[str, Any]                  # HTTP请求参数
    
    # 工具
    _mdconvert: MarkdownConverter                   # Markdown转换器
```

#### WebPageInfo.page_read_info_list (List[PageReadInfo]) 详细结构
```python
class PageReadInfo:
    def __init__(self, search_results_idx: int, url: str, page_title: str, 
                 fetch_res: str, page_thinking: str, page_summary: str,
                 page_number: int, need_page_down: bool, used: bool = False):
        self.search_results_idx: int    # 搜索结果索引
        self.url: str                   # 页面URL
        self.page_title: str            # 页面标题
        self.fetch_res: str             # 获取的页面内容
        self.page_thinking: str         # 页面思考过程
        self.page_summary: str          # 页面摘要
        self.page_number: int           # 页面编号
        self.need_page_down: bool       # 是否需要向下翻页
        self.used: bool                 # 是否已被使用（默认False）
```

## 实际数据示例

### 输入示例
```python
user_query = "Python编程语言的特点"
search_query_list = ["Python programming features", "Python language characteristics"]
api_result_dict = {
    "Python programming features": {
        "organic": [
            {"title": "Python Features", "link": "https://example1.com", "snippet": "Python is..."},
            {"title": "Why Python", "link": "https://example2.com", "snippet": "Python offers..."}
        ]
    },
    "Python language characteristics": {
        "organic": [
            {"title": "Python Characteristics", "link": "https://example3.com", "snippet": "Key features..."}
        ]
    }
}
```

### 输出示例结构
```python
[
    # 第一个搜索查询 "Python programming features" 的结果
    [
        WebPageInfo(
            title="Python Features",
            url="https://example1.com",
            quick_summary="Python is...",
            browser=None,  # 初始状态为None
            sub_question="Python programming features",
            page_read_info_list=[]  # 初始状态为空列表
        ),
        WebPageInfo(
            title="Why Python",
            url="https://example2.com", 
            quick_summary="Python offers...",
            browser=None,
            sub_question="Python programming features",
            page_read_info_list=[]
        )
    ],
    # 第二个搜索查询 "Python language characteristics" 的结果
    [
        WebPageInfo(
            title="Python Characteristics",
            url="https://example3.com",
            quick_summary="Key features...",
            browser=None,
            sub_question="Python language characteristics", 
            page_read_info_list=[]
        )
    ]
]
```

## 重要说明

### 初始状态
- `browser` 属性在 `search_web_batch` 返回时为 `None`
- `page_read_info_list` 在初始状态为空列表 `[]`
- 这些属性会在后续的网页访问和阅读过程中被填充

### 数据流转
1. **search_web_batch** → 返回基本的WebPageInfo列表
2. **scrape_and_check_valid** → 填充browser属性
3. **reading_agent.read** → 填充page_read_info_list属性

### 访问模式
```python
# 访问第一个搜索查询的第一个网页
first_webpage = result[0][0]
print(first_webpage.title)        # 网页标题
print(first_webpage.url)          # 网页URL  
print(first_webpage.quick_summary) # 快速摘要
print(first_webpage.sub_question)  # 搜索查询

# 如果browser已被填充
if first_webpage.browser:
    print(first_webpage.browser.page_title)    # 页面标题
    print(first_webpage.browser.page_content)  # 完整页面内容
    print(len(first_webpage.browser.viewport_pages))  # 视口页面数量

# 如果page_read_info_list已被填充
for page_info in first_webpage.page_read_info_list:
    print(page_info.page_summary)  # 页面摘要
    print(page_info.page_number)   # 页面编号
```

## 类型注解总结
```python
search_web_batch() -> List[List[WebPageInfo]]
                      ↓
                   List[WebPageInfo]  # 单个搜索查询的结果
                      ↓  
                   WebPageInfo        # 单个网页信息
                      ├── title: str
                      ├── url: str  
                      ├── quick_summary: str
                      ├── sub_question: str
                      ├── browser: SimpleTextBrowser | None
                      └── page_read_info_list: List[PageReadInfo]
                             ↓
                          PageReadInfo  # 页面阅读详细信息
                             ├── search_results_idx: int
                             ├── url: str
                             ├── page_title: str
                             ├── fetch_res: str
                             ├── page_thinking: str
                             ├── page_summary: str
                             ├── page_number: int
                             ├── need_page_down: bool
                             └── used: bool
```
