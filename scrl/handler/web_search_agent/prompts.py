
QUICK_SUMMARY_PROMPT = """You are an AI assistant analyzing webpage content to determine if it's helpful for answering a user's question. Given:

1. User query: {user_query}
2. Search query: {search_query}
3. Webpage content: {first_page_fetch_res}

Evaluate if this webpage contains useful information for answering the user's question or search query.

Think through:
1. What key information does the webpage content contain?
2. How does this information relate to the user's question or search query?
3. Is the content sufficient and relevant to help answer the query?

Provide your analysis in this format:
<helpful>yes/no</helpful>
<summary>If helpful: Concise summary of relevant information that helps answer the query</summary>"""

WIKIPEDIA_CONTENT_EXTRACTION_PROMPT = """You are an AI assistant analyzing Wikipedia content to extract and summarize information relevant to the search query. Given:


1. Search query: {search_query}
2. Wikipedia content: {wikipedia_content}

Your task is to extract and summarize the most relevant information from the Wikipedia content that could help answer the search query.

Think through:
1. What key information in the Wikipedia content is directly relevant to the queries?
2. What background information or context might be helpful for understanding the topic?
3. Are there any specific facts, dates, relationships, or details that address the search query or the user question?
4. What are the most important points that should be highlighted?

Provide your analysis in this format:
<helpful>yes/no</helpful>
<summary>If helpful: Concise summary of relevant information extracted from Wikipedia that helps answer the search query. Include key facts, important details, and relevant context. Organize the information clearly and concisely.</summary>"""
